import type { ThemeConfig } from "../types/theme";

const THEME_DATA_ATTRIBUTES = ["data-theme", "data-mode"] as const;

let isFirstApplication = true;

const getPreferredMode = (mode: ThemeConfig["mode"]): "dark" | "light" => {
  if (mode === "dark" || mode === "light") return mode;
  return window.matchMedia?.("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";
};

const clearThemeAttributes = (el: HTMLElement): void => {
  for (const attr of THEME_DATA_ATTRIBUTES) {
    el.removeAttribute(attr);
  }
};

export function applyTheme(config: ThemeConfig): void {
  if (typeof document === "undefined") return;

  const root = document.documentElement;

  // Prevent transitions during first theme application
  if (isFirstApplication) {
    root.classList.add("no-transition");
    isFirstApplication = false;
  }

  clearThemeAttributes(root);

  const mode = getPreferredMode(config.mode);
  if (config.variant && config.variant !== "default") {
    root.setAttribute("data-theme", config.variant);
  }

  root.setAttribute("data-mode", mode);

  // Restore transitions
  if (root.classList.contains("no-transition")) {
    requestAnimationFrame(() => root.classList.remove("no-transition"));
  }
}
